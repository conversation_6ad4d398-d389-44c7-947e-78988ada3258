// Test script to verify custom time blocks functionality
const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:3000';

async function testCustomTimeBlocks() {
  console.log('🧪 Testing Custom Time Blocks Functionality...\n');

  let cookies = '';

  try {
    // Test 1: Login with test user
    console.log('1. Logging in with test user...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });

    console.log(`   Login status: ${loginResponse.status}`);

    if (loginResponse.status === 200) {
      // Extract cookies for subsequent requests
      const setCookieHeader = loginResponse.headers.get('set-cookie');
      if (setCookieHeader) {
        cookies = setCookieHeader;
        console.log('   ✅ Login successful');
      }
    } else {
      const errorData = await loginResponse.json();
      console.log('   ❌ Login failed:', errorData);
      return;
    }

    // Test 2: Get current profile/preferences
    console.log('\n2. Getting current preferences...');
    const profileResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
    });

    console.log(`   Profile status: ${profileResponse.status}`);

    if (profileResponse.status === 200) {
      const profileData = await profileResponse.json();
      console.log('   Full profile response:', JSON.stringify(profileData, null, 2));
      console.log('   Current preferences:', JSON.stringify(profileData.data?.preferences || {}, null, 2));
      console.log('   ✅ Profile retrieved successfully');
    } else {
      console.log('   ❌ Failed to get profile');
      return;
    }

    // Test 3: Update preferences with custom time blocks
    console.log('\n3. Testing custom time blocks update...');

    const testPreferences = {
      timeInterval: '60',
      startHour: '0',
      endHour: '23',
      timeFormat: '24',
      darkMode: false,
      syncEnabled: false,
      customTimeBlocks: [
        { startTime: '09:00', endTime: '12:00' },
        { startTime: '14:00', endTime: '17:00' }
      ],
      useCustomTimeBlocks: true
    };

    console.log('   Updating preferences with custom time blocks...');
    const updateResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
      body: JSON.stringify({
        preferences: testPreferences
      })
    });

    console.log(`   Update status: ${updateResponse.status}`);

    if (updateResponse.status === 200) {
      const updateData = await updateResponse.json();
      console.log('   Full update response:', JSON.stringify(updateData, null, 2));
      console.log('   Updated preferences:', JSON.stringify(updateData.data?.preferences || {}, null, 2));
      console.log('   ✅ Preferences updated successfully');
    } else {
      const errorData = await updateResponse.json();
      console.log('   ❌ Failed to update preferences:', errorData);
      return;
    }

    // Test 4: Verify preferences persist by fetching again
    console.log('\n4. Verifying preferences persistence...');
    const verifyResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies
      },
    });

    if (verifyResponse.status === 200) {
      const verifyData = await verifyResponse.json();
      console.log('   Full verify response:', JSON.stringify(verifyData, null, 2));
      const savedPrefs = verifyData.data?.preferences || {};

      console.log('   Saved preferences:', JSON.stringify(savedPrefs, null, 2));

      // Check if custom time blocks were saved
      if (savedPrefs.customTimeBlocks && savedPrefs.customTimeBlocks.length > 0) {
        console.log('   ✅ Custom time blocks were saved successfully');
        console.log(`   ✅ Found ${savedPrefs.customTimeBlocks.length} custom time blocks`);
      } else {
        console.log('   ❌ Custom time blocks were not saved');
      }

      // Check if useCustomTimeBlocks flag was saved
      if (savedPrefs.useCustomTimeBlocks === true) {
        console.log('   ✅ useCustomTimeBlocks flag was saved successfully');
      } else {
        console.log('   ❌ useCustomTimeBlocks flag was not saved correctly');
      }
    } else {
      console.log('   ❌ Failed to verify preferences');
    }

    console.log('\n✅ All tests completed!');
    console.log('\n📋 Manual testing steps:');
    console.log('   1. Open http://localhost:3000 in your browser');
    console.log('   2. <NAME_EMAIL> / password123');
    console.log('   3. Navigate to Settings > Preferences');
    console.log('   4. Check if custom time blocks are already there');
    console.log('   5. Go to Home and check if grid view shows custom time blocks');
    console.log('   6. Try adding/removing custom time blocks and saving');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testCustomTimeBlocks();
